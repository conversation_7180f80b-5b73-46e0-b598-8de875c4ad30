<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Tableau de bord - Administration CMC';

// Récupérer les statistiques
try {
    // Nombre d'actualités
    $stmt = $db->query("SELECT COUNT(*) as total FROM actualites");
    $total_actualites = $stmt->fetch()['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM actualites WHERE statut = 'publie'");
    $actualites_publiees = $stmt->fetch()['total'];
    
    // Nombre de formations
    $stmt = $db->query("SELECT COUNT(*) as total FROM formations");
    $total_formations = $stmt->fetch()['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM formations WHERE statut = 'active'");
    $formations_actives = $stmt->fetch()['total'];
    
    // Nombre d'événements
    $stmt = $db->query("SELECT COUNT(*) as total FROM evenements");
    $total_evenements = $stmt->fetch()['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM evenements WHERE statut = 'planifie'");
    $evenements_planifies = $stmt->fetch()['total'];
    
    // Nombre d'utilisateurs
    $stmt = $db->query("SELECT COUNT(*) as total FROM utilisateurs");
    $total_utilisateurs = $stmt->fetch()['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM utilisateurs WHERE statut = 'actif'");
    $utilisateurs_actifs = $stmt->fetch()['total'];
    
    // Dernières actualités
    $stmt = $db->query("SELECT * FROM actualites ORDER BY created_at DESC LIMIT 5");
    $dernieres_actualites = $stmt->fetchAll();
    
    // Prochains événements
    $stmt = $db->query("SELECT * FROM evenements WHERE date_debut >= NOW() ORDER BY date_debut ASC LIMIT 5");
    $prochains_evenements = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = "Erreur lors de la récupération des données: " . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-tachometer-alt"></i>
            Tableau de bord
        </h1>
        <div>
            <span class="text-gray">Dernière connexion : <?php echo date('d/m/Y à H:i'); ?></span>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-newspaper stat-icon"></i>
            <span class="stat-number"><?php echo $total_actualites; ?></span>
            <span class="stat-label">Actualités</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $actualites_publiees; ?> publiées
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-graduation-cap stat-icon"></i>
            <span class="stat-number"><?php echo $total_formations; ?></span>
            <span class="stat-label">Formations</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $formations_actives; ?> actives
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-calendar-alt stat-icon"></i>
            <span class="stat-number"><?php echo $total_evenements; ?></span>
            <span class="stat-label">Événements</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--info);">
                <?php echo $evenements_planifies; ?> planifiés
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-users stat-icon"></i>
            <span class="stat-number"><?php echo $total_utilisateurs; ?></span>
            <span class="stat-label">Utilisateurs</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $utilisateurs_actifs; ?> actifs
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="admin-card">
        <h2 class="card-title">Actions rapides</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
            <a href="actualites.php?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouvelle actualité
            </a>
            <a href="formations.php?action=add" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Nouvelle formation
            </a>
            <a href="evenements.php?action=add" class="btn btn-info">
                <i class="fas fa-plus"></i>
                Nouvel événement
            </a>
            <a href="utilisateurs.php?action=add" class="btn btn-warning">
                <i class="fas fa-plus"></i>
                Nouvel utilisateur
            </a>
        </div>
    </div>

    <!-- Contenu récent -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-top: 2rem;">
        <!-- Dernières actualités -->
        <div class="admin-card">
            <div class="card-header">
                <h3 class="card-title">Dernières actualités</h3>
                <a href="actualites.php" class="btn btn-sm btn-secondary">Voir tout</a>
            </div>
            
            <?php if (empty($dernieres_actualites)): ?>
                <p style="text-align: center; color: var(--gray); padding: 2rem;">
                    <i class="fas fa-newspaper" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                    Aucune actualité pour le moment
                </p>
            <?php else: ?>
                <div style="space-y: 1rem;">
                    <?php foreach ($dernieres_actualites as $actualite): ?>
                        <div style="padding: 1rem; border-left: 4px solid var(--primary-color); background: var(--light-green); margin-bottom: 1rem; border-radius: 0 var(--border-radius) var(--border-radius) 0;">
                            <h4 style="margin-bottom: 0.5rem; font-size: 1rem;">
                                <?php echo htmlspecialchars($actualite['titre']); ?>
                            </h4>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: var(--gray);">
                                <span class="status-badge status-<?php echo $actualite['statut']; ?>">
                                    <?php echo ucfirst($actualite['statut']); ?>
                                </span>
                                <span><?php echo date('d/m/Y', strtotime($actualite['created_at'])); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Prochains événements -->
        <div class="admin-card">
            <div class="card-header">
                <h3 class="card-title">Prochains événements</h3>
                <a href="evenements.php" class="btn btn-sm btn-secondary">Voir tout</a>
            </div>
            
            <?php if (empty($prochains_evenements)): ?>
                <p style="text-align: center; color: var(--gray); padding: 2rem;">
                    <i class="fas fa-calendar-alt" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                    Aucun événement planifié
                </p>
            <?php else: ?>
                <div style="space-y: 1rem;">
                    <?php foreach ($prochains_evenements as $evenement): ?>
                        <div style="padding: 1rem; border-left: 4px solid var(--info); background: #e3f2fd; margin-bottom: 1rem; border-radius: 0 var(--border-radius) var(--border-radius) 0;">
                            <h4 style="margin-bottom: 0.5rem; font-size: 1rem;">
                                <?php echo htmlspecialchars($evenement['titre']); ?>
                            </h4>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.9rem; color: var(--gray);">
                                <span>
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo htmlspecialchars($evenement['lieu']); ?>
                                </span>
                                <span>
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('d/m/Y', strtotime($evenement['date_debut'])); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Informations système -->
    <div class="admin-card" style="margin-top: 2rem;">
        <h3 class="card-title">Informations système</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-top: 1rem;">
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem;">Serveur</h4>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <strong>PHP :</strong> <?php echo PHP_VERSION; ?>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <strong>Serveur :</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'N/A'; ?>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <strong>Base de données :</strong> MySQL
                    </li>
                </ul>
            </div>
            
            <div>
                <h4 style="color: var(--primary-color); margin-bottom: 1rem;">Utilisateur</h4>
                <ul style="list-style: none; padding: 0;">
                    <li style="margin-bottom: 0.5rem;">
                        <strong>Nom :</strong> <?php echo getCurrentUser()['username']; ?>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <strong>Email :</strong> <?php echo getCurrentUser()['email']; ?>
                    </li>
                    <li style="margin-bottom: 0.5rem;">
                        <strong>Rôle :</strong> <?php echo getCurrentUser()['role'] === 'admin' ? 'Administrateur' : 'Éditeur'; ?>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
