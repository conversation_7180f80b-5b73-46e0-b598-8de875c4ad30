<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Tableau de bord - Administration CMC';

// Récupérer les statistiques
try {
    // Nombre d'actualités
    $stmt = $db->query("SELECT COUNT(*) as total FROM actualites");
    $total_actualites = $stmt->fetch()['total'];

    $stmt = $db->query("SELECT COUNT(*) as total FROM actualites WHERE statut = 'publie'");
    $actualites_publiees = $stmt->fetch()['total'];

    // Nombre de formations
    $stmt = $db->query("SELECT COUNT(*) as total FROM formations");
    $total_formations = $stmt->fetch()['total'];

    $stmt = $db->query("SELECT COUNT(*) as total FROM formations WHERE statut = 'active'");
    $formations_actives = $stmt->fetch()['total'];

    // Nombre d'événements
    $stmt = $db->query("SELECT COUNT(*) as total FROM evenements");
    $total_evenements = $stmt->fetch()['total'];

    $stmt = $db->query("SELECT COUNT(*) as total FROM evenements WHERE statut = 'planifie'");
    $evenements_planifies = $stmt->fetch()['total'];

    // Nombre d'utilisateurs
    $stmt = $db->query("SELECT COUNT(*) as total FROM utilisateurs");
    $total_utilisateurs = $stmt->fetch()['total'];

    $stmt = $db->query("SELECT COUNT(*) as total FROM utilisateurs WHERE statut = 'actif'");
    $utilisateurs_actifs = $stmt->fetch()['total'];

} catch(PDOException $e) {
    $total_actualites = $actualites_publiees = 0;
    $total_formations = $formations_actives = 0;
    $total_evenements = $evenements_planifies = 0;
    $total_utilisateurs = $utilisateurs_actifs = 0;
}

include 'includes/header.php';
?>

<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-tachometer-alt"></i>
            Tableau de bord
        </h1>
        <div>
            <span style="color: var(--gray);">Dernière connexion : <?php echo date('d/m/Y à H:i'); ?></span>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-newspaper stat-icon"></i>
            <span class="stat-number"><?php echo $total_actualites; ?></span>
            <span class="stat-label">Actualités</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $actualites_publiees; ?> publiées
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-graduation-cap stat-icon"></i>
            <span class="stat-number"><?php echo $total_formations; ?></span>
            <span class="stat-label">Formations</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $formations_actives; ?> actives
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-calendar-alt stat-icon"></i>
            <span class="stat-number"><?php echo $total_evenements; ?></span>
            <span class="stat-label">Événements</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--info);">
                <?php echo $evenements_planifies; ?> planifiés
            </div>
        </div>

        <div class="stat-card">
            <i class="fas fa-users stat-icon"></i>
            <span class="stat-number"><?php echo $total_utilisateurs; ?></span>
            <span class="stat-label">Utilisateurs</span>
            <div style="margin-top: 0.5rem; font-size: 0.9rem; color: var(--success);">
                <?php echo $utilisateurs_actifs; ?> actifs
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="admin-card">
        <h2 class="card-title">Actions rapides</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 2rem;">
            <a href="actualites.php?action=add" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Nouvelle actualité
            </a>
            <a href="formations.php?action=add" class="btn btn-success">
                <i class="fas fa-plus"></i>
                Nouvelle formation
            </a>
            <a href="evenements.php?action=add" class="btn btn-info">
                <i class="fas fa-plus"></i>
                Nouvel événement
            </a>
            <a href="utilisateurs.php?action=add" class="btn btn-warning">
                <i class="fas fa-plus"></i>
                Nouvel utilisateur
            </a>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
