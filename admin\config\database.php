<?php
// Configuration de la base de données
class Database {
    private $host = 'localhost';
    private $db_name = 'cmc_agriculture';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8")
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Erreur de connexion: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    // Créer les tables si elles n'existent pas
    public function createTables() {
        $sql = "
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'editor') DEFAULT 'editor',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS actualites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            contenu TEXT NOT NULL,
            image VARCHAR(255),
            categorie ENUM('innovation', 'partenariat', 'formation', 'evenement', 'conference', 'visite') DEFAULT 'formation',
            statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
            date_publication DATE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(id)
        );

        CREATE TABLE IF NOT EXISTS formations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            duree VARCHAR(50) NOT NULL,
            type_diplome ENUM('certificat', 'diplome') DEFAULT 'certificat',
            categorie ENUM('culture', 'elevage', 'gestion', 'durable') NOT NULL,
            image VARCHAR(255),
            prix DECIMAL(10,2),
            places_disponibles INT DEFAULT 0,
            statut ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS evenements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            date_debut DATETIME NOT NULL,
            date_fin DATETIME NOT NULL,
            lieu VARCHAR(255) NOT NULL,
            type_evenement ENUM('conference', 'portes_ouvertes', 'formation', 'visite', 'autre') DEFAULT 'autre',
            image VARCHAR(255),
            statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS emplois_temps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            formation_id INT,
            groupe VARCHAR(10) NOT NULL,
            jour_semaine ENUM('lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi') NOT NULL,
            heure_debut TIME NOT NULL,
            heure_fin TIME NOT NULL,
            matiere VARCHAR(100) NOT NULL,
            professeur VARCHAR(100) NOT NULL,
            salle VARCHAR(50) NOT NULL,
            type_cours ENUM('cours', 'tp', 'pratique', 'projet') DEFAULT 'cours',
            semaine_debut DATE NOT NULL,
            semaine_fin DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (formation_id) REFERENCES formations(id)
        );

        CREATE TABLE IF NOT EXISTS utilisateurs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(100) NOT NULL,
            prenom VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            telephone VARCHAR(20),
            formation_id INT,
            groupe VARCHAR(10),
            statut ENUM('actif', 'inactif', 'diplome') DEFAULT 'actif',
            date_inscription DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (formation_id) REFERENCES formations(id)
        );

        -- Insérer un utilisateur admin par défaut
        INSERT IGNORE INTO admin_users (username, email, password, role) 
        VALUES ('admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'admin');
        ";

        try {
            $this->conn->exec($sql);
            return true;
        } catch(PDOException $exception) {
            echo "Erreur lors de la création des tables: " . $exception->getMessage();
            return false;
        }
    }
}

// Initialiser la base de données
$database = new Database();
$db = $database->getConnection();

// Créer les tables si elles n'existent pas
if ($db) {
    $database->createTables();
}
?>
