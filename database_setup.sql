-- Script de création de la base de données CMC Agriculture
-- Exécuter ce script dans phpMyAdmin ou ligne de commande MySQL

-- Créer la base de données
CREATE DATABASE IF NOT EXISTS cmc_agriculture CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE cmc_agriculture;

-- Table des utilisateurs administrateurs
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des actualités
CREATE TABLE IF NOT EXISTS actualites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    contenu TEXT NOT NULL,
    image VARCHAR(255),
    categorie ENUM('innovation', 'partenariat', 'formation', 'evenement', 'conference', 'visite') DEFAULT 'formation',
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    date_publication DATE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Table des formations
CREATE TABLE IF NOT EXISTS formations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duree VARCHAR(50) NOT NULL,
    type_diplome ENUM('certificat', 'diplome') DEFAULT 'certificat',
    categorie ENUM('culture', 'elevage', 'gestion', 'durable') NOT NULL,
    image VARCHAR(255),
    prix DECIMAL(10,2),
    places_disponibles INT DEFAULT 0,
    statut ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des événements
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    lieu VARCHAR(255) NOT NULL,
    type_evenement ENUM('conference', 'portes_ouvertes', 'formation', 'visite', 'autre') DEFAULT 'autre',
    image VARCHAR(255),
    statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des emplois du temps
CREATE TABLE IF NOT EXISTS emplois_temps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    formation_id INT,
    groupe VARCHAR(10) NOT NULL,
    jour_semaine ENUM('lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi') NOT NULL,
    heure_debut TIME NOT NULL,
    heure_fin TIME NOT NULL,
    matiere VARCHAR(100) NOT NULL,
    professeur VARCHAR(100) NOT NULL,
    salle VARCHAR(50) NOT NULL,
    type_cours ENUM('cours', 'tp', 'pratique', 'projet') DEFAULT 'cours',
    semaine_debut DATE NOT NULL,
    semaine_fin DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE
);

-- Table des utilisateurs/étudiants
CREATE TABLE IF NOT EXISTS utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    telephone VARCHAR(20),
    formation_id INT,
    groupe VARCHAR(10),
    statut ENUM('actif', 'inactif', 'diplome') DEFAULT 'actif',
    date_inscription DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE SET NULL
);

-- Insérer l'utilisateur admin par défaut
INSERT INTO admin_users (username, email, password, role) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- Insérer des données de test pour les formations
INSERT INTO formations (nom, description, duree, type_diplome, categorie, prix, places_disponibles, statut) VALUES
('Technicien en Production Végétale', 'Formation complète sur les techniques de production végétale, de la préparation du sol à la récolte.', '12 mois', 'diplome', 'culture', 15000.00, 25, 'active'),
('Technicien Spécialisé en Élevage', 'Maîtrisez les techniques modernes d\'élevage bovin, ovin et avicole pour optimiser la production.', '18 mois', 'diplome', 'elevage', 18000.00, 20, 'active'),
('Gestion d\'exploitation agricole', 'Apprenez à gérer efficacement une exploitation agricole : comptabilité, marketing, ressources humaines.', '10 mois', 'certificat', 'gestion', 12000.00, 30, 'active'),
('Techniques de maraîchage', 'Spécialisez-vous dans la production de légumes frais avec les techniques les plus modernes.', '8 mois', 'certificat', 'culture', 10000.00, 15, 'active'),
('Micronutrition agricole', 'Découvrez les secrets de la nutrition des plantes pour optimiser les rendements naturellement.', '6 mois', 'certificat', 'durable', 8000.00, 20, 'active'),
('Transformation des produits agricoles', 'Apprenez à valoriser vos productions par la transformation et la création de produits à valeur ajoutée.', '9 mois', 'diplome', 'durable', 14000.00, 18, 'active')
ON DUPLICATE KEY UPDATE nom = nom;

-- Insérer des données de test pour les actualités
INSERT INTO actualites (titre, contenu, categorie, statut, date_publication, created_by) VALUES
('Inauguration du nouveau laboratoire d\'analyse des sols', 'Le Pôle Agriculture inaugure son nouveau laboratoire d\'analyse des sols, équipé des technologies les plus avancées pour former nos étudiants aux méthodes modernes d\'analyse. Ce laboratoire permettra aux étudiants de se familiariser avec les dernières techniques d\'analyse chimique et biologique des sols.', 'innovation', 'publie', '2025-01-15', 1),
('Nouveau partenariat avec la coopérative COPAG', 'Signature d\'un accord de partenariat stratégique avec COPAG pour offrir des stages pratiques et des opportunités d\'emploi à nos diplômés. Ce partenariat permettra à nos étudiants de bénéficier d\'une expérience pratique directement sur le terrain.', 'partenariat', 'publie', '2025-01-10', 1),
('Lancement du programme d\'agriculture durable', 'Découvrez notre nouveau programme de formation axé sur les pratiques agricoles durables et respectueuses de l\'environnement. Cette formation innovante répond aux défis actuels de l\'agriculture moderne.', 'formation', 'publie', '2025-01-05', 1),
('Succès de la première promotion de techniciens en élevage', 'Félicitations à nos 25 nouveaux diplômés en techniques d\'élevage qui ont brillamment réussi leur formation avec un taux de réussite de 96%. Ces nouveaux professionnels sont maintenant prêts à contribuer au développement du secteur agricole.', 'evenement', 'publie', '2024-12-28', 1),
('Visite du Ministre de l\'Agriculture', 'Le Ministre de l\'Agriculture a visité nos installations et salué la qualité de nos formations et l\'excellence de nos équipements. Cette visite officielle confirme la reconnaissance de notre établissement au niveau national.', 'visite', 'publie', '2024-12-15', 1)
ON DUPLICATE KEY UPDATE titre = titre;

-- Insérer des données de test pour les événements
INSERT INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut) VALUES
('Journée Portes Ouvertes 2025', 'Venez découvrir nos installations, rencontrer nos équipes pédagogiques et vous informer sur nos formations. Une occasion unique de visiter nos laboratoires et notre ferme pédagogique.', '2025-03-15 09:00:00', '2025-03-15 17:00:00', 'Campus CMC Agriculture', 'portes_ouvertes', 'planifie'),
('Conférence sur l\'Agriculture du Futur', 'Conférence internationale sur les innovations technologiques dans l\'agriculture moderne. Intervenants experts nationaux et internationaux.', '2025-04-20 14:00:00', '2025-04-20 18:00:00', 'Amphithéâtre principal', 'conference', 'planifie'),
('Salon de l\'Agriculture Durable', 'Participation au salon national de l\'agriculture durable avec présentation de nos projets étudiants et démonstrations pratiques.', '2025-05-10 08:00:00', '2025-05-12 18:00:00', 'Centre d\'exposition de Casablanca', 'autre', 'planifie'),
('Formation continue : Nouvelles technologies agricoles', 'Session de formation continue destinée aux professionnels du secteur agricole sur l\'utilisation des drones et capteurs en agriculture.', '2025-02-25 09:00:00', '2025-02-27 17:00:00', 'Laboratoire d\'innovation', 'formation', 'planifie')
ON DUPLICATE KEY UPDATE titre = titre;

-- Insérer des données de test pour les utilisateurs
INSERT INTO utilisateurs (nom, prenom, email, telephone, formation_id, groupe, statut, date_inscription) VALUES
('Benali', 'Ahmed', '<EMAIL>', '+212 6XX XX XX XX', 1, 'Groupe A', 'actif', '2024-09-15'),
('Alami', 'Fatima', '<EMAIL>', '+212 6XX XX XX XX', 2, 'Groupe A', 'actif', '2024-09-15'),
('Tazi', 'Mohammed', '<EMAIL>', '+212 6XX XX XX XX', 1, 'Groupe B', 'actif', '2024-09-15'),
('Zahra', 'Aicha', '<EMAIL>', '+212 6XX XX XX XX', 3, 'Groupe A', 'actif', '2024-09-20'),
('Idrissi', 'Youssef', '<EMAIL>', '+212 6XX XX XX XX', 4, 'Groupe A', 'actif', '2024-10-01')
ON DUPLICATE KEY UPDATE nom = nom;

-- Insérer des données de test pour les emplois du temps
INSERT INTO emplois_temps (formation_id, groupe, jour_semaine, heure_debut, heure_fin, matiere, professeur, salle, type_cours, semaine_debut, semaine_fin) VALUES
(1, 'Groupe A', 'lundi', '08:00:00', '10:00:00', 'Agronomie', 'Prof. Benali', 'A101', 'cours', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'lundi', '10:15:00', '12:15:00', 'Chimie des sols', 'Prof. Benali', 'Labo A', 'tp', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'mardi', '08:00:00', '10:00:00', 'Biologie végétale', 'Prof. Alami', 'Labo B', 'tp', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'mardi', '10:15:00', '12:15:00', 'TP Irrigation', 'Prof. Alami', 'Terrain', 'pratique', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'mercredi', '08:00:00', '10:00:00', 'Pratique terrain', 'Prof. Tazi', 'Ferme pédagogique', 'pratique', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'jeudi', '08:00:00', '10:00:00', 'Gestion', 'Prof. Tazi', 'C201', 'cours', '2025-01-06', '2025-06-30'),
(1, 'Groupe A', 'vendredi', '08:00:00', '10:00:00', 'Phytopathologie', 'Prof. Zahra', 'A102', 'cours', '2025-01-06', '2025-06-30')
ON DUPLICATE KEY UPDATE matiere = matiere;

-- Afficher un message de confirmation
SELECT 'Base de données CMC Agriculture créée avec succès !' as Message;
SELECT 'Utilisateur admin créé : admin / admin123' as Connexion;
SELECT 'Données de test insérées' as Donnees;
