<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Gestion des utilisateurs - Administration CMC';

// Traitement des actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $nom = trim($_POST['nom'] ?? '');
        $prenom = trim($_POST['prenom'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $telephone = trim($_POST['telephone'] ?? '');
        $formation_id = $_POST['formation_id'] ?? null;
        $groupe = trim($_POST['groupe'] ?? '');
        $statut = $_POST['statut'] ?? 'actif';
        $date_inscription = $_POST['date_inscription'] ?? date('Y-m-d');
        
        if (empty($nom) || empty($prenom) || empty($email)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO utilisateurs (nom, prenom, email, telephone, formation_id, groupe, statut, date_inscription) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$nom, $prenom, $email, $telephone, $formation_id, $groupe, $statut, $date_inscription]);
                    header('Location: utilisateurs.php?success=created');
                } else {
                    $id = $_POST['id'];
                    $stmt = $db->prepare("UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, telephone = ?, formation_id = ?, groupe = ?, statut = ?, date_inscription = ? WHERE id = ?");
                    $stmt->execute([$nom, $prenom, $email, $telephone, $formation_id, $groupe, $statut, $date_inscription, $id]);
                    header('Location: utilisateurs.php?success=updated');
                }
                exit();
            } catch(PDOException $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    }
}

// Suppression
if (isset($_GET['delete'])) {
    try {
        $stmt = $db->prepare("DELETE FROM utilisateurs WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        header('Location: utilisateurs.php?success=deleted');
        exit();
    } catch(PDOException $e) {
        $error = 'Erreur lors de la suppression: ' . $e->getMessage();
    }
}

// Récupération des données
$action = $_GET['action'] ?? 'list';
$utilisateur = null;

if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $db->prepare("SELECT * FROM utilisateurs WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $utilisateur = $stmt->fetch();
    
    if (!$utilisateur) {
        header('Location: utilisateurs.php?error=not_found');
        exit();
    }
}

// Liste des utilisateurs avec formations
if ($action === 'list') {
    $stmt = $db->query("
        SELECT u.*, f.nom as formation_nom 
        FROM utilisateurs u 
        LEFT JOIN formations f ON u.formation_id = f.id 
        ORDER BY u.created_at DESC
    ");
    $utilisateurs = $stmt->fetchAll();
}

// Liste des formations pour le formulaire
$stmt = $db->query("SELECT id, nom FROM formations WHERE statut = 'active' ORDER BY nom");
$formations = $stmt->fetchAll();

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- Liste des utilisateurs -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-users"></i>
            Gestion des utilisateurs
        </h1>
        <a href="utilisateurs.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Ajouter un utilisateur
        </a>
    </div>

    <!-- Filtres et recherche -->
    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
        <input type="text" id="search-input" placeholder="Rechercher..." class="form-control" style="max-width: 300px;">
        <select class="form-control form-select filter-select" data-filter="formation" style="max-width: 250px;">
            <option value="">Toutes les formations</option>
            <?php foreach ($formations as $formation): ?>
                <option value="<?php echo $formation['id']; ?>"><?php echo htmlspecialchars($formation['nom']); ?></option>
            <?php endforeach; ?>
        </select>
        <select class="form-control form-select filter-select" data-filter="statut" style="max-width: 150px;">
            <option value="">Tous les statuts</option>
            <option value="actif">Actif</option>
            <option value="inactif">Inactif</option>
            <option value="diplome">Diplômé</option>
        </select>
    </div>

    <!-- Tableau des utilisateurs -->
    <table class="admin-table">
        <thead>
            <tr>
                <th>Utilisateur</th>
                <th>Contact</th>
                <th>Formation</th>
                <th>Groupe</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($utilisateurs)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-users" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                        Aucun utilisateur pour le moment
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($utilisateurs as $item): ?>
                    <tr data-formation="<?php echo $item['formation_id']; ?>" data-statut="<?php echo $item['statut']; ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($item['prenom'] . ' ' . $item['nom']); ?></strong>
                            <div style="font-size: 0.9rem; color: var(--gray); margin-top: 0.25rem;">
                                Inscrit le <?php echo date('d/m/Y', strtotime($item['date_inscription'])); ?>
                            </div>
                        </td>
                        <td>
                            <div><?php echo htmlspecialchars($item['email']); ?></div>
                            <?php if ($item['telephone']): ?>
                                <div style="font-size: 0.9rem; color: var(--gray);">
                                    <?php echo htmlspecialchars($item['telephone']); ?>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($item['formation_nom']): ?>
                                <span class="status-badge" style="background: var(--light-green); color: var(--primary-color);">
                                    <?php echo htmlspecialchars($item['formation_nom']); ?>
                                </span>
                            <?php else: ?>
                                <span style="color: var(--gray);">Aucune</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($item['groupe']): ?>
                                <span style="font-weight: 600;"><?php echo htmlspecialchars($item['groupe']); ?></span>
                            <?php else: ?>
                                <span style="color: var(--gray);">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $status_colors = [
                                'actif' => 'success',
                                'inactif' => 'danger',
                                'diplome' => 'info'
                            ];
                            $color = $status_colors[$item['statut']] ?? 'secondary';
                            ?>
                            <span class="status-badge" style="background: var(--<?php echo $color; ?>); color: var(--white);">
                                <?php echo ucfirst($item['statut']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="utilisateurs.php?action=edit&id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="utilisateurs.php?delete=<?php echo $item['id']; ?>" 
                                   class="btn btn-sm btn-danger" 
                                   data-action="delete" 
                                   data-item="cet utilisateur">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php else: ?>
<!-- Formulaire d'ajout/modification -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action === 'add' ? 'Ajouter un utilisateur' : 'Modifier l\'utilisateur'; ?>
        </h1>
        <a href="utilisateurs.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <form method="POST" action="">
        <input type="hidden" name="action" value="<?php echo $action; ?>">
        <?php if ($action === 'edit'): ?>
            <input type="hidden" name="id" value="<?php echo $utilisateur['id']; ?>">
        <?php endif; ?>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
            <div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="prenom" class="form-label">Prénom *</label>
                        <input type="text" 
                               id="prenom" 
                               name="prenom" 
                               class="form-control" 
                               required
                               value="<?php echo htmlspecialchars($utilisateur['prenom'] ?? ''); ?>"
                               placeholder="Prénom">
                    </div>

                    <div class="form-group">
                        <label for="nom" class="form-label">Nom *</label>
                        <input type="text" 
                               id="nom" 
                               name="nom" 
                               class="form-control" 
                               required
                               value="<?php echo htmlspecialchars($utilisateur['nom'] ?? ''); ?>"
                               placeholder="Nom de famille">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email *</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($utilisateur['email'] ?? ''); ?>"
                           placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="telephone" class="form-label">Téléphone</label>
                    <input type="tel" 
                           id="telephone" 
                           name="telephone" 
                           class="form-control"
                           value="<?php echo htmlspecialchars($utilisateur['telephone'] ?? ''); ?>"
                           placeholder="+212 6XX XX XX XX">
                </div>

                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="formation_id" class="form-label">Formation</label>
                        <select id="formation_id" name="formation_id" class="form-control form-select">
                            <option value="">Aucune formation</option>
                            <?php foreach ($formations as $formation): ?>
                                <option value="<?php echo $formation['id']; ?>" 
                                        <?php echo ($utilisateur['formation_id'] ?? '') == $formation['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($formation['nom']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="groupe" class="form-label">Groupe</label>
                        <select id="groupe" name="groupe" class="form-control form-select">
                            <option value="">Aucun groupe</option>
                            <option value="Groupe A" <?php echo ($utilisateur['groupe'] ?? '') === 'Groupe A' ? 'selected' : ''; ?>>Groupe A</option>
                            <option value="Groupe B" <?php echo ($utilisateur['groupe'] ?? '') === 'Groupe B' ? 'selected' : ''; ?>>Groupe B</option>
                            <option value="Groupe C" <?php echo ($utilisateur['groupe'] ?? '') === 'Groupe C' ? 'selected' : ''; ?>>Groupe C</option>
                        </select>
                    </div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label for="statut" class="form-label">Statut</label>
                    <select id="statut" name="statut" class="form-control form-select">
                        <option value="actif" <?php echo ($utilisateur['statut'] ?? '') === 'actif' ? 'selected' : ''; ?>>Actif</option>
                        <option value="inactif" <?php echo ($utilisateur['statut'] ?? '') === 'inactif' ? 'selected' : ''; ?>>Inactif</option>
                        <option value="diplome" <?php echo ($utilisateur['statut'] ?? '') === 'diplome' ? 'selected' : ''; ?>>Diplômé</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="date_inscription" class="form-label">Date d'inscription</label>
                    <input type="date" 
                           id="date_inscription" 
                           name="date_inscription" 
                           class="form-control"
                           value="<?php echo $utilisateur['date_inscription'] ?? date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer l\'utilisateur' : 'Mettre à jour'; ?>
                    </button>
                </div>

                <?php if ($action === 'edit'): ?>
                    <div class="form-group">
                        <small style="color: var(--gray);">
                            Créé le <?php echo date('d/m/Y à H:i', strtotime($utilisateur['created_at'])); ?>
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
