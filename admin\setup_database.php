<?php
/**
 * Script d'initialisation de la base de données CMC Agriculture
 * À exécuter une seule fois pour créer la base de données et les tables
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'cmc_agriculture';

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Installation Base de Données - CMC Agriculture</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #4a7c59; background: #f8f9fa; }
        h1 { color: #4a7c59; }
        code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🗄️ Installation Base de Données CMC Agriculture</h1>";

try {
    // Étape 1: Connexion à MySQL sans base de données
    echo "<div class='step'><h3>Étape 1: Connexion à MySQL</h3>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✅ Connexion à MySQL réussie</div>";
    
    // Étape 2: Création de la base de données
    echo "<h3>Étape 2: Création de la base de données</h3>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");
    echo "<div class='success'>✅ Base de données '$database' créée</div>";
    
    // Étape 3: Sélection de la base de données
    echo "<h3>Étape 3: Sélection de la base de données</h3>";
    $pdo->exec("USE $database");
    echo "<div class='success'>✅ Base de données sélectionnée</div>";
    
    // Étape 4: Création des tables
    echo "<h3>Étape 4: Création des tables</h3>";
    
    // Table admin_users
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role ENUM('admin', 'editor') DEFAULT 'editor',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<div class='success'>✅ Table 'admin_users' créée</div>";
    
    // Table actualites
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS actualites (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            contenu TEXT NOT NULL,
            image VARCHAR(255),
            categorie ENUM('innovation', 'partenariat', 'formation', 'evenement', 'conference', 'visite') DEFAULT 'formation',
            statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
            date_publication DATE,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL
        )
    ");
    echo "<div class='success'>✅ Table 'actualites' créée</div>";
    
    // Table formations
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS formations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            duree VARCHAR(50) NOT NULL,
            type_diplome ENUM('certificat', 'diplome') DEFAULT 'certificat',
            categorie ENUM('culture', 'elevage', 'gestion', 'durable') NOT NULL,
            image VARCHAR(255),
            prix DECIMAL(10,2),
            places_disponibles INT DEFAULT 0,
            statut ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<div class='success'>✅ Table 'formations' créée</div>";
    
    // Table evenements
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS evenements (
            id INT AUTO_INCREMENT PRIMARY KEY,
            titre VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            date_debut DATETIME NOT NULL,
            date_fin DATETIME,
            lieu VARCHAR(255) NOT NULL,
            type_evenement ENUM('conference', 'portes_ouvertes', 'formation', 'visite', 'autre') DEFAULT 'autre',
            image VARCHAR(255),
            statut ENUM('planifie', 'en_cours', 'termine', 'annule') DEFAULT 'planifie',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<div class='success'>✅ Table 'evenements' créée</div>";
    
    // Table emplois_temps
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS emplois_temps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            formation_id INT,
            groupe VARCHAR(10) NOT NULL,
            jour_semaine ENUM('lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi') NOT NULL,
            heure_debut TIME NOT NULL,
            heure_fin TIME NOT NULL,
            matiere VARCHAR(100) NOT NULL,
            professeur VARCHAR(100) NOT NULL,
            salle VARCHAR(50) NOT NULL,
            type_cours ENUM('cours', 'tp', 'pratique', 'projet') DEFAULT 'cours',
            semaine_debut DATE NOT NULL,
            semaine_fin DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE
        )
    ");
    echo "<div class='success'>✅ Table 'emplois_temps' créée</div>";
    
    // Table utilisateurs
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS utilisateurs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(100) NOT NULL,
            prenom VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            telephone VARCHAR(20),
            formation_id INT,
            groupe VARCHAR(10),
            statut ENUM('actif', 'inactif', 'diplome') DEFAULT 'actif',
            date_inscription DATE DEFAULT (CURRENT_DATE),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE SET NULL
        )
    ");
    echo "<div class='success'>✅ Table 'utilisateurs' créée</div>";
    
    // Étape 5: Insertion de l'utilisateur admin
    echo "<h3>Étape 5: Création de l'utilisateur administrateur</h3>";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, email, password, role) VALUES (?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'admin']);
    echo "<div class='success'>✅ Utilisateur admin créé</div>";
    echo "<div class='info'>📝 <strong>Identifiants de connexion :</strong><br>
          Utilisateur: <code>admin</code><br>
          Mot de passe: <code>admin123</code></div>";
    
    // Étape 6: Insertion des données de test
    echo "<h3>Étape 6: Insertion des données de test</h3>";
    
    // Formations de test
    $formations = [
        ['Technicien en Production Végétale', 'Formation complète sur les techniques de production végétale, de la préparation du sol à la récolte.', '12 mois', 'diplome', 'culture', 15000.00, 25],
        ['Technicien Spécialisé en Élevage', 'Maîtrisez les techniques modernes d\'élevage bovin, ovin et avicole pour optimiser la production.', '18 mois', 'diplome', 'elevage', 18000.00, 20],
        ['Gestion d\'exploitation agricole', 'Apprenez à gérer efficacement une exploitation agricole : comptabilité, marketing, ressources humaines.', '10 mois', 'certificat', 'gestion', 12000.00, 30],
        ['Techniques de maraîchage', 'Spécialisez-vous dans la production de légumes frais avec les techniques les plus modernes.', '8 mois', 'certificat', 'culture', 10000.00, 15],
        ['Micronutrition agricole', 'Découvrez les secrets de la nutrition des plantes pour optimiser les rendements naturellement.', '6 mois', 'certificat', 'durable', 8000.00, 20],
        ['Transformation des produits agricoles', 'Apprenez à valoriser vos productions par la transformation et la création de produits à valeur ajoutée.', '9 mois', 'diplome', 'durable', 14000.00, 18]
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO formations (nom, description, duree, type_diplome, categorie, prix, places_disponibles, statut) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')");
    foreach ($formations as $formation) {
        $stmt->execute($formation);
    }
    echo "<div class='success'>✅ " . count($formations) . " formations de test ajoutées</div>";
    
    // Actualités de test
    $actualites = [
        ['Inauguration du nouveau laboratoire d\'analyse des sols', 'Le Pôle Agriculture inaugure son nouveau laboratoire d\'analyse des sols, équipé des technologies les plus avancées pour former nos étudiants aux méthodes modernes d\'analyse.', 'innovation', 'publie', '2025-01-15'],
        ['Nouveau partenariat avec la coopérative COPAG', 'Signature d\'un accord de partenariat stratégique avec COPAG pour offrir des stages pratiques et des opportunités d\'emploi à nos diplômés.', 'partenariat', 'publie', '2025-01-10'],
        ['Lancement du programme d\'agriculture durable', 'Découvrez notre nouveau programme de formation axé sur les pratiques agricoles durables et respectueuses de l\'environnement.', 'formation', 'publie', '2025-01-05'],
        ['Succès de la première promotion de techniciens en élevage', 'Félicitations à nos 25 nouveaux diplômés en techniques d\'élevage qui ont brillamment réussi leur formation avec un taux de réussite de 96%.', 'evenement', 'publie', '2024-12-28'],
        ['Visite du Ministre de l\'Agriculture', 'Le Ministre de l\'Agriculture a visité nos installations et salué la qualité de nos formations et l\'excellence de nos équipements.', 'visite', 'publie', '2024-12-15']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO actualites (titre, contenu, categorie, statut, date_publication, created_by) VALUES (?, ?, ?, ?, ?, 1)");
    foreach ($actualites as $actualite) {
        $stmt->execute($actualite);
    }
    echo "<div class='success'>✅ " . count($actualites) . " actualités de test ajoutées</div>";
    
    echo "</div>";
    
    // Résumé final
    echo "<div class='step'>
            <h3>🎉 Installation terminée avec succès !</h3>
            <div class='success'>
                <strong>Base de données créée :</strong> $database<br>
                <strong>Tables créées :</strong> 6 tables<br>
                <strong>Données de test :</strong> Insérées<br>
                <strong>Utilisateur admin :</strong> Créé
            </div>
            <div class='info'>
                <h4>Prochaines étapes :</h4>
                <ol>
                    <li>Accédez à l'administration : <a href='login.php'>http://localhost:8000/admin/login.php</a></li>
                    <li>Connectez-vous avec : <code>admin</code> / <code>admin123</code></li>
                    <li>Commencez à gérer votre contenu !</li>
                </ol>
            </div>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ Erreur : " . $e->getMessage() . "</div>";
    echo "<div class='info'>
            <h4>Solutions possibles :</h4>
            <ul>
                <li>Vérifiez que MySQL/XAMPP est démarré</li>
                <li>Vérifiez les paramètres de connexion dans le script</li>
                <li>Assurez-vous que l'utilisateur 'root' a les permissions nécessaires</li>
            </ul>
          </div>";
}

echo "</body></html>";
?>
