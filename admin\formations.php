<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Gestion des formations - Administration CMC';

// Traitement des actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $nom = trim($_POST['nom'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $duree = trim($_POST['duree'] ?? '');
        $type_diplome = $_POST['type_diplome'] ?? 'certificat';
        $categorie = $_POST['categorie'] ?? 'culture';
        $prix = $_POST['prix'] ?? null;
        $places_disponibles = $_POST['places_disponibles'] ?? 0;
        $statut = $_POST['statut'] ?? 'active';
        
        if (empty($nom) || empty($description) || empty($duree)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO formations (nom, description, duree, type_diplome, categorie, prix, places_disponibles, statut) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$nom, $description, $duree, $type_diplome, $categorie, $prix, $places_disponibles, $statut]);
                    header('Location: formations.php?success=created');
                } else {
                    $id = $_POST['id'];
                    $stmt = $db->prepare("UPDATE formations SET nom = ?, description = ?, duree = ?, type_diplome = ?, categorie = ?, prix = ?, places_disponibles = ?, statut = ? WHERE id = ?");
                    $stmt->execute([$nom, $description, $duree, $type_diplome, $categorie, $prix, $places_disponibles, $statut, $id]);
                    header('Location: formations.php?success=updated');
                }
                exit();
            } catch(PDOException $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    }
}

// Suppression
if (isset($_GET['delete'])) {
    try {
        $stmt = $db->prepare("DELETE FROM formations WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        header('Location: formations.php?success=deleted');
        exit();
    } catch(PDOException $e) {
        $error = 'Erreur lors de la suppression: ' . $e->getMessage();
    }
}

// Récupération des données
$action = $_GET['action'] ?? 'list';
$formation = null;

if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $db->prepare("SELECT * FROM formations WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $formation = $stmt->fetch();
    
    if (!$formation) {
        header('Location: formations.php?error=not_found');
        exit();
    }
}

// Liste des formations
if ($action === 'list') {
    $stmt = $db->query("SELECT * FROM formations ORDER BY created_at DESC");
    $formations = $stmt->fetchAll();
}

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- Liste des formations -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-graduation-cap"></i>
            Gestion des formations
        </h1>
        <a href="formations.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Ajouter une formation
        </a>
    </div>

    <!-- Filtres et recherche -->
    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
        <input type="text" id="search-input" placeholder="Rechercher..." class="form-control" style="max-width: 300px;">
        <select class="form-control form-select filter-select" data-filter="categorie" style="max-width: 200px;">
            <option value="">Toutes les catégories</option>
            <option value="culture">Culture</option>
            <option value="elevage">Élevage</option>
            <option value="gestion">Gestion</option>
            <option value="durable">Agriculture durable</option>
        </select>
        <select class="form-control form-select filter-select" data-filter="statut" style="max-width: 150px;">
            <option value="">Tous les statuts</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
        </select>
    </div>

    <!-- Tableau des formations -->
    <table class="admin-table">
        <thead>
            <tr>
                <th>Formation</th>
                <th>Durée</th>
                <th>Catégorie</th>
                <th>Places</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($formations)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-graduation-cap" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                        Aucune formation pour le moment
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($formations as $item): ?>
                    <tr data-categorie="<?php echo $item['categorie']; ?>" data-statut="<?php echo $item['statut']; ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($item['nom']); ?></strong>
                            <div style="font-size: 0.9rem; color: var(--gray); margin-top: 0.25rem;">
                                <?php echo substr(strip_tags($item['description']), 0, 80) . '...'; ?>
                            </div>
                            <div style="font-size: 0.8rem; color: var(--primary-color); margin-top: 0.25rem;">
                                <?php echo ucfirst($item['type_diplome']); ?>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($item['duree']); ?></td>
                        <td>
                            <span class="status-badge" style="background: var(--light-green); color: var(--primary-color);">
                                <?php echo ucfirst($item['categorie']); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($item['places_disponibles'] > 0): ?>
                                <span style="color: var(--success);"><?php echo $item['places_disponibles']; ?> places</span>
                            <?php else: ?>
                                <span style="color: var(--danger);">Complet</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $item['statut']; ?>">
                                <?php echo ucfirst($item['statut']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="formations.php?action=edit&id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="formations.php?delete=<?php echo $item['id']; ?>" 
                                   class="btn btn-sm btn-danger" 
                                   data-action="delete" 
                                   data-item="cette formation">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php else: ?>
<!-- Formulaire d'ajout/modification -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action === 'add' ? 'Ajouter une formation' : 'Modifier la formation'; ?>
        </h1>
        <a href="formations.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <form method="POST" action="">
        <input type="hidden" name="action" value="<?php echo $action; ?>">
        <?php if ($action === 'edit'): ?>
            <input type="hidden" name="id" value="<?php echo $formation['id']; ?>">
        <?php endif; ?>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
            <div>
                <div class="form-group">
                    <label for="nom" class="form-label">Nom de la formation *</label>
                    <input type="text" 
                           id="nom" 
                           name="nom" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($formation['nom'] ?? ''); ?>"
                           placeholder="Ex: Technicien en Production Végétale">
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Description *</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control" 
                              required
                              rows="10"
                              placeholder="Description détaillée de la formation..."><?php echo htmlspecialchars($formation['description'] ?? ''); ?></textarea>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="duree" class="form-label">Durée *</label>
                        <input type="text" 
                               id="duree" 
                               name="duree" 
                               class="form-control" 
                               required
                               value="<?php echo htmlspecialchars($formation['duree'] ?? ''); ?>"
                               placeholder="Ex: 12 mois">
                    </div>

                    <div class="form-group">
                        <label for="prix" class="form-label">Prix (DH)</label>
                        <input type="number" 
                               id="prix" 
                               name="prix" 
                               class="form-control" 
                               step="0.01"
                               value="<?php echo $formation['prix'] ?? ''; ?>"
                               placeholder="0.00">
                    </div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label for="statut" class="form-label">Statut</label>
                    <select id="statut" name="statut" class="form-control form-select">
                        <option value="active" <?php echo ($formation['statut'] ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo ($formation['statut'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="categorie" class="form-label">Catégorie</label>
                    <select id="categorie" name="categorie" class="form-control form-select">
                        <option value="culture" <?php echo ($formation['categorie'] ?? '') === 'culture' ? 'selected' : ''; ?>>Culture</option>
                        <option value="elevage" <?php echo ($formation['categorie'] ?? '') === 'elevage' ? 'selected' : ''; ?>>Élevage</option>
                        <option value="gestion" <?php echo ($formation['categorie'] ?? '') === 'gestion' ? 'selected' : ''; ?>>Gestion</option>
                        <option value="durable" <?php echo ($formation['categorie'] ?? '') === 'durable' ? 'selected' : ''; ?>>Agriculture durable</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="type_diplome" class="form-label">Type de diplôme</label>
                    <select id="type_diplome" name="type_diplome" class="form-control form-select">
                        <option value="certificat" <?php echo ($formation['type_diplome'] ?? '') === 'certificat' ? 'selected' : ''; ?>>Certificat</option>
                        <option value="diplome" <?php echo ($formation['type_diplome'] ?? '') === 'diplome' ? 'selected' : ''; ?>>Diplôme</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="places_disponibles" class="form-label">Places disponibles</label>
                    <input type="number" 
                           id="places_disponibles" 
                           name="places_disponibles" 
                           class="form-control" 
                           min="0"
                           value="<?php echo $formation['places_disponibles'] ?? '0'; ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer la formation' : 'Mettre à jour'; ?>
                    </button>
                </div>

                <?php if ($action === 'edit'): ?>
                    <div class="form-group">
                        <small style="color: var(--gray);">
                            Créé le <?php echo date('d/m/Y à H:i', strtotime($formation['created_at'])); ?>
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
