<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'Administration - CMC Agriculture'; ?></title>
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php if (isLoggedIn()): ?>
    <header class="admin-header">
        <div class="header-container">
            <div class="header-left">
                <img src="../images/logo-cmc.svg" alt="Logo CMC" class="admin-logo">
                <h1>Interface d'administration</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user"></i>
                        <?php echo getCurrentUser()['username']; ?>
                    </span>
                    <span class="user-role">
                        <?php echo getCurrentUser()['role'] === 'admin' ? 'Administrateur' : 'Éditeur'; ?>
                    </span>
                </div>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Se déconnecter
                </a>
            </div>
        </div>
    </header>

    <nav class="admin-nav">
        <div class="nav-container">
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.php" class="nav-link" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        Voir le site
                    </a>
                </li>
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        Tableau de bord
                    </a>
                </li>
                <li class="nav-item">
                    <a href="actualites.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'actualites.php' ? 'active' : ''; ?>">
                        <i class="fas fa-newspaper"></i>
                        Actualités
                    </a>
                </li>
                <li class="nav-item">
                    <a href="formations.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'formations.php' ? 'active' : ''; ?>">
                        <i class="fas fa-graduation-cap"></i>
                        Formations
                    </a>
                </li>
                <li class="nav-item">
                    <a href="evenements.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'evenements.php' ? 'active' : ''; ?>">
                        <i class="fas fa-calendar-alt"></i>
                        Événements
                    </a>
                </li>
                <li class="nav-item">
                    <a href="emplois-temps.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'emplois-temps.php' ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i>
                        Emplois du temps
                    </a>
                </li>
                <li class="nav-item">
                    <a href="utilisateurs.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'utilisateurs.php' ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i>
                        Utilisateurs
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    <?php endif; ?>

    <main class="admin-main">
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php 
                switch($_GET['success']) {
                    case 'created': echo 'Élément créé avec succès !'; break;
                    case 'updated': echo 'Élément mis à jour avec succès !'; break;
                    case 'deleted': echo 'Élément supprimé avec succès !'; break;
                    case 'login': echo 'Connexion réussie !'; break;
                    default: echo 'Opération réussie !';
                }
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <?php 
                switch($_GET['error']) {
                    case 'access_denied': echo 'Accès refusé !'; break;
                    case 'not_found': echo 'Élément non trouvé !'; break;
                    case 'database': echo 'Erreur de base de données !'; break;
                    case 'login_failed': echo 'Identifiants incorrects !'; break;
                    default: echo 'Une erreur est survenue !';
                }
                ?>
            </div>
        <?php endif; ?>
