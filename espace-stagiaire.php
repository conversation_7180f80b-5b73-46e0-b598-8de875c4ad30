<?php 
$page_title = "Espace Stagiaire - Pôle Agriculture CMC";
include 'includes/header.php'; 
?>

<main>
    <!-- Hero Section -->
    <section class="hero" style="padding: 120px 0 60px;">
        <div class="container">
            <div class="text-center">
                <h1 style="font-size: 3rem; margin-bottom: 1rem;">Espace Stagiaire</h1>
                <p style="font-size: 1.2rem; opacity: 0.9; max-width: 600px; margin: 0 auto;">
                    Consultez l'emploi du temps de votre formation.
                </p>
            </div>
        </div>
    </section>

    <!-- Sélection filière et groupe -->
    <section class="section" style="background: var(--light-gray);">
        <div class="container">
            <div style="max-width: 600px; margin: 0 auto; background: var(--white); padding: 3rem; border-radius: var(--border-radius); box-shadow: var(--shadow-lg);">
                <div class="text-center mb-4">
                    <h2 style="color: var(--dark-gray); margin-bottom: 1rem;">Sélectionnez votre filière et groupe</h2>
                    <p style="color: var(--gray);">
                        Veuillez choisir votre filière de formation et votre groupe pour 
                        accéder à votre emploi du temps
                    </p>
                </div>

                <form id="student-form">
                    <div class="form-group">
                        <label for="filiere" class="form-label">Filière</label>
                        <select id="filiere" class="form-control form-select" required>
                            <option value="">Sélectionnez votre filière</option>
                            <option value="production-vegetale">Technicien en Production Végétale</option>
                            <option value="elevage">Technicien Spécialisé en Élevage</option>
                            <option value="gestion">Gestion d'exploitation agricole</option>
                            <option value="maraichage">Techniques de maraîchage</option>
                            <option value="micronutrition">Micronutrition agricole</option>
                            <option value="transformation">Transformation des produits agricoles</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="groupe" class="form-label">Groupe</label>
                        <select id="groupe" class="form-control form-select" required>
                            <option value="">Sélectionnez votre groupe</option>
                            <option value="groupe-a">Groupe A</option>
                            <option value="groupe-b">Groupe B</option>
                            <option value="groupe-c">Groupe C</option>
                        </select>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-green">
                            <i class="fas fa-calendar-alt"></i> Consulter l'emploi du temps
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Emploi du temps (caché par défaut) -->
    <section id="emploi-du-temps" class="section" style="display: none;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Emploi du temps</h2>
                <p class="section-subtitle">Semaine du 16 au 20 Juin 2025</p>
            </div>

            <!-- Navigation semaine -->
            <div class="text-center mb-4">
                <button class="btn btn-secondary" id="prev-week">
                    <i class="fas fa-chevron-left"></i> Semaine précédente
                </button>
                <span style="margin: 0 2rem; font-weight: 600; font-size: 1.1rem;" id="current-week">
                    Semaine du 16 au 20 Juin 2025
                </span>
                <button class="btn btn-secondary" id="next-week">
                    Semaine suivante <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <!-- Tableau emploi du temps -->
            <div style="overflow-x: auto;">
                <table style="width: 100%; background: var(--white); border-radius: var(--border-radius); box-shadow: var(--shadow); border-collapse: collapse;">
                    <thead>
                        <tr style="background: var(--primary-color); color: var(--white);">
                            <th style="padding: 1rem; text-align: left; border-right: 1px solid rgba(255,255,255,0.2);">Horaires</th>
                            <th style="padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);">Lundi</th>
                            <th style="padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);">Mardi</th>
                            <th style="padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);">Mercredi</th>
                            <th style="padding: 1rem; text-align: center; border-right: 1px solid rgba(255,255,255,0.2);">Jeudi</th>
                            <th style="padding: 1rem; text-align: center;">Vendredi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 1rem; font-weight: 600; background: var(--light-green);">08:00 - 10:00</td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e3f2fd; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #2196f3;">
                                    <strong>Agronomie</strong><br>
                                    <small>Salle A101 - Prof. Benali</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #f3e5f5; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #9c27b0;">
                                    <strong>Biologie végétale</strong><br>
                                    <small>Labo B - Prof. Alami</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 4px; border-left: 4px solid var(--primary-color);">
                                    <strong>Pratique terrain</strong><br>
                                    <small>Ferme pédagogique</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fff3e0; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #ff9800;">
                                    <strong>Gestion</strong><br>
                                    <small>Salle C201 - Prof. Tazi</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fce4ec; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #e91e63;">
                                    <strong>Phytopathologie</strong><br>
                                    <small>Salle A102 - Prof. Zahra</small>
                                </div>
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 1rem; font-weight: 600; background: var(--light-green);">10:15 - 12:15</td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #f3e5f5; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #9c27b0;">
                                    <strong>Chimie des sols</strong><br>
                                    <small>Labo A - Prof. Benali</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 4px; border-left: 4px solid var(--primary-color);">
                                    <strong>TP Irrigation</strong><br>
                                    <small>Terrain - Prof. Alami</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e3f2fd; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #2196f3;">
                                    <strong>Mécanisation</strong><br>
                                    <small>Atelier - Prof. Tazi</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fce4ec; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #e91e63;">
                                    <strong>Entomologie</strong><br>
                                    <small>Labo C - Prof. Zahra</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fff3e0; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #ff9800;">
                                    <strong>Économie agricole</strong><br>
                                    <small>Salle B101 - Prof. Benali</small>
                                </div>
                            </td>
                        </tr>
                        <tr style="background: var(--light-gray);">
                            <td style="padding: 1rem; font-weight: 600; text-align: center;" colspan="6">
                                <i class="fas fa-utensils"></i> PAUSE DÉJEUNER (12:15 - 14:00)
                            </td>
                        </tr>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 1rem; font-weight: 600; background: var(--light-green);">14:00 - 16:00</td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 4px; border-left: 4px solid var(--primary-color);">
                                    <strong>Stage pratique</strong><br>
                                    <small>Exploitation partenaire</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fff3e0; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #ff9800;">
                                    <strong>Comptabilité</strong><br>
                                    <small>Salle informatique</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #f3e5f5; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #9c27b0;">
                                    <strong>Analyse laboratoire</strong><br>
                                    <small>Labo principal</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e3f2fd; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #2196f3;">
                                    <strong>Projet tutoré</strong><br>
                                    <small>Salle projet</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center; color: var(--gray);">
                                <i class="fas fa-times"></i> Libre
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 1rem; font-weight: 600; background: var(--light-green);">16:15 - 18:15</td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e8f5e8; padding: 0.5rem; border-radius: 4px; border-left: 4px solid var(--primary-color);">
                                    <strong>Stage pratique</strong><br>
                                    <small>Exploitation partenaire</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #fce4ec; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #e91e63;">
                                    <strong>Soutien scolaire</strong><br>
                                    <small>Salle A101</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #f3e5f5; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #9c27b0;">
                                    <strong>Analyse laboratoire</strong><br>
                                    <small>Labo principal</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <div style="background: #e3f2fd; padding: 0.5rem; border-radius: 4px; border-left: 4px solid #2196f3;">
                                    <strong>Projet tutoré</strong><br>
                                    <small>Salle projet</small>
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center; color: var(--gray);">
                                <i class="fas fa-times"></i> Libre
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Légende -->
            <div style="margin-top: 2rem; background: var(--white); padding: 2rem; border-radius: var(--border-radius); box-shadow: var(--shadow);">
                <h3 style="margin-bottom: 1rem; color: var(--dark-gray);">Légende</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 2px;"></div>
                        <span>Cours théoriques</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #f3e5f5; border-left: 4px solid #9c27b0; border-radius: 2px;"></div>
                        <span>Travaux pratiques</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #e8f5e8; border-left: 4px solid var(--primary-color); border-radius: 2px;"></div>
                        <span>Pratique terrain</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #fff3e0; border-left: 4px solid #ff9800; border-radius: 2px;"></div>
                        <span>Gestion/Économie</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 20px; height: 20px; background: #fce4ec; border-left: 4px solid #e91e63; border-radius: 2px;"></div>
                        <span>Sciences appliquées</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Informations pratiques -->
    <section class="section" style="background: var(--light-green);">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Informations pratiques</h2>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div style="background: var(--white); padding: 2rem; border-radius: var(--border-radius); box-shadow: var(--shadow);">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-clock"></i> Horaires
                    </h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><strong>Matin :</strong> 08:00 - 12:15</li>
                        <li style="margin-bottom: 0.5rem;"><strong>Après-midi :</strong> 14:00 - 18:15</li>
                        <li style="margin-bottom: 0.5rem;"><strong>Pause déjeuner :</strong> 12:15 - 14:00</li>
                        <li><strong>Pause :</strong> 10:00 - 10:15 et 16:00 - 16:15</li>
                    </ul>
                </div>

                <div style="background: var(--white); padding: 2rem; border-radius: var(--border-radius); box-shadow: var(--shadow);">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-map-marker-alt"></i> Localisation
                    </h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><strong>Bâtiment A :</strong> Salles de cours</li>
                        <li style="margin-bottom: 0.5rem;"><strong>Bâtiment B :</strong> Laboratoires</li>
                        <li style="margin-bottom: 0.5rem;"><strong>Bâtiment C :</strong> Ateliers pratiques</li>
                        <li><strong>Ferme :</strong> Travaux pratiques terrain</li>
                    </ul>
                </div>

                <div style="background: var(--white); padding: 2rem; border-radius: var(--border-radius); box-shadow: var(--shadow);">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">
                        <i class="fas fa-phone"></i> Contact
                    </h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><strong>Scolarité :</strong> +212 5XX XX XX XX</li>
                        <li style="margin-bottom: 0.5rem;"><strong>Email :</strong> <EMAIL></li>
                        <li style="margin-bottom: 0.5rem;"><strong>Urgence :</strong> +212 6XX XX XX XX</li>
                        <li><strong>Accueil :</strong> Ouvert 7h30 - 18h30</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</main>

<script>
// Afficher l'emploi du temps après sélection
document.getElementById('student-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const filiere = document.getElementById('filiere').value;
    const groupe = document.getElementById('groupe').value;
    
    if (filiere && groupe) {
        document.getElementById('emploi-du-temps').style.display = 'block';
        document.getElementById('emploi-du-temps').scrollIntoView({ behavior: 'smooth' });
    }
});

// Navigation semaines
let currentWeek = 0;
const weeks = [
    'Semaine du 16 au 20 Juin 2025',
    'Semaine du 23 au 27 Juin 2025',
    'Semaine du 30 Juin au 4 Juillet 2025',
    'Semaine du 7 au 11 Juillet 2025'
];

document.getElementById('prev-week').addEventListener('click', function() {
    if (currentWeek > 0) {
        currentWeek--;
        document.getElementById('current-week').textContent = weeks[currentWeek];
    }
});

document.getElementById('next-week').addEventListener('click', function() {
    if (currentWeek < weeks.length - 1) {
        currentWeek++;
        document.getElementById('current-week').textContent = weeks[currentWeek];
    }
});
</script>

<?php include 'includes/footer.php'; ?>
