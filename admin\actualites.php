<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Gestion des actualités - Administration CMC';

// Traitement des actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $titre = trim($_POST['titre'] ?? '');
        $contenu = trim($_POST['contenu'] ?? '');
        $categorie = $_POST['categorie'] ?? 'formation';
        $statut = $_POST['statut'] ?? 'brouillon';
        $date_publication = $_POST['date_publication'] ?? null;
        
        if (empty($titre) || empty($contenu)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO actualites (titre, contenu, categorie, statut, date_publication, created_by) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$titre, $contenu, $categorie, $statut, $date_publication, getCurrentUser()['id']]);
                    header('Location: actualites.php?success=created');
                } else {
                    $id = $_POST['id'];
                    $stmt = $db->prepare("UPDATE actualites SET titre = ?, contenu = ?, categorie = ?, statut = ?, date_publication = ? WHERE id = ?");
                    $stmt->execute([$titre, $contenu, $categorie, $statut, $date_publication, $id]);
                    header('Location: actualites.php?success=updated');
                }
                exit();
            } catch(PDOException $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    }
}

// Suppression
if (isset($_GET['delete'])) {
    try {
        $stmt = $db->prepare("DELETE FROM actualites WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        header('Location: actualites.php?success=deleted');
        exit();
    } catch(PDOException $e) {
        $error = 'Erreur lors de la suppression: ' . $e->getMessage();
    }
}

// Récupération des données
$action = $_GET['action'] ?? 'list';
$actualite = null;

if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $db->prepare("SELECT * FROM actualites WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $actualite = $stmt->fetch();
    
    if (!$actualite) {
        header('Location: actualites.php?error=not_found');
        exit();
    }
}

// Liste des actualités
if ($action === 'list') {
    $stmt = $db->query("SELECT * FROM actualites ORDER BY created_at DESC");
    $actualites = $stmt->fetchAll();
}

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- Liste des actualités -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-newspaper"></i>
            Gestion des actualités
        </h1>
        <a href="actualites.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Ajouter une actualité
        </a>
    </div>

    <!-- Filtres et recherche -->
    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
        <input type="text" id="search-input" placeholder="Rechercher..." class="form-control" style="max-width: 300px;">
        <select class="form-control form-select filter-select" data-filter="categorie" style="max-width: 200px;">
            <option value="">Toutes les catégories</option>
            <option value="innovation">Innovation</option>
            <option value="partenariat">Partenariat</option>
            <option value="formation">Formation</option>
            <option value="evenement">Événement</option>
            <option value="conference">Conférence</option>
            <option value="visite">Visite officielle</option>
        </select>
        <select class="form-control form-select filter-select" data-filter="statut" style="max-width: 150px;">
            <option value="">Tous les statuts</option>
            <option value="brouillon">Brouillon</option>
            <option value="publie">Publié</option>
        </select>
    </div>

    <!-- Tableau des actualités -->
    <table class="admin-table">
        <thead>
            <tr>
                <th>Titre</th>
                <th>Date</th>
                <th>Catégorie</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($actualites)): ?>
                <tr>
                    <td colspan="5" style="text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-newspaper" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                        Aucune actualité pour le moment
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($actualites as $item): ?>
                    <tr data-categorie="<?php echo $item['categorie']; ?>" data-statut="<?php echo $item['statut']; ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($item['titre']); ?></strong>
                            <div style="font-size: 0.9rem; color: var(--gray); margin-top: 0.25rem;">
                                <?php echo substr(strip_tags($item['contenu']), 0, 100) . '...'; ?>
                            </div>
                        </td>
                        <td>
                            <?php if ($item['date_publication']): ?>
                                <?php echo date('d/m/Y', strtotime($item['date_publication'])); ?>
                            <?php else: ?>
                                <span style="color: var(--gray);">Non définie</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-badge" style="background: var(--light-green); color: var(--primary-color);">
                                <?php echo ucfirst($item['categorie']); ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $item['statut']; ?>">
                                <?php echo ucfirst($item['statut']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="actualites.php?action=edit&id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="actualites.php?delete=<?php echo $item['id']; ?>" 
                                   class="btn btn-sm btn-danger" 
                                   data-action="delete" 
                                   data-item="cette actualité">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php else: ?>
<!-- Formulaire d'ajout/modification -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action === 'add' ? 'Ajouter une actualité' : 'Modifier l\'actualité'; ?>
        </h1>
        <a href="actualites.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <form method="POST" action="">
        <input type="hidden" name="action" value="<?php echo $action; ?>">
        <?php if ($action === 'edit'): ?>
            <input type="hidden" name="id" value="<?php echo $actualite['id']; ?>">
        <?php endif; ?>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
            <div>
                <div class="form-group">
                    <label for="titre" class="form-label">Titre *</label>
                    <input type="text" 
                           id="titre" 
                           name="titre" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($actualite['titre'] ?? ''); ?>"
                           placeholder="Titre de l'actualité">
                </div>

                <div class="form-group">
                    <label for="contenu" class="form-label">Contenu *</label>
                    <textarea id="contenu" 
                              name="contenu" 
                              class="form-control" 
                              required
                              rows="15"
                              placeholder="Contenu de l'actualité..."><?php echo htmlspecialchars($actualite['contenu'] ?? ''); ?></textarea>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label for="statut" class="form-label">Statut</label>
                    <select id="statut" name="statut" class="form-control form-select">
                        <option value="brouillon" <?php echo ($actualite['statut'] ?? '') === 'brouillon' ? 'selected' : ''; ?>>Brouillon</option>
                        <option value="publie" <?php echo ($actualite['statut'] ?? '') === 'publie' ? 'selected' : ''; ?>>Publié</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="categorie" class="form-label">Catégorie</label>
                    <select id="categorie" name="categorie" class="form-control form-select">
                        <option value="innovation" <?php echo ($actualite['categorie'] ?? '') === 'innovation' ? 'selected' : ''; ?>>Innovation</option>
                        <option value="partenariat" <?php echo ($actualite['categorie'] ?? '') === 'partenariat' ? 'selected' : ''; ?>>Partenariat</option>
                        <option value="formation" <?php echo ($actualite['categorie'] ?? '') === 'formation' ? 'selected' : ''; ?>>Formation</option>
                        <option value="evenement" <?php echo ($actualite['categorie'] ?? '') === 'evenement' ? 'selected' : ''; ?>>Événement</option>
                        <option value="conference" <?php echo ($actualite['categorie'] ?? '') === 'conference' ? 'selected' : ''; ?>>Conférence</option>
                        <option value="visite" <?php echo ($actualite['categorie'] ?? '') === 'visite' ? 'selected' : ''; ?>>Visite officielle</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="date_publication" class="form-label">Date de publication</label>
                    <input type="date" 
                           id="date_publication" 
                           name="date_publication" 
                           class="form-control"
                           value="<?php echo $actualite['date_publication'] ?? ''; ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer l\'actualité' : 'Mettre à jour'; ?>
                    </button>
                </div>

                <?php if ($action === 'edit'): ?>
                    <div class="form-group">
                        <small style="color: var(--gray);">
                            Créé le <?php echo date('d/m/Y à H:i', strtotime($actualite['created_at'])); ?>
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
