<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Gestion des événements - Administration CMC';

// Traitement des actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $titre = trim($_POST['titre'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $date_debut = $_POST['date_debut'] ?? '';
        $date_fin = $_POST['date_fin'] ?? '';
        $lieu = trim($_POST['lieu'] ?? '');
        $type_evenement = $_POST['type_evenement'] ?? 'autre';
        $statut = $_POST['statut'] ?? 'planifie';
        
        if (empty($titre) || empty($description) || empty($date_debut) || empty($lieu)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO evenements (titre, description, date_debut, date_fin, lieu, type_evenement, statut) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$titre, $description, $date_debut, $date_fin, $lieu, $type_evenement, $statut]);
                    header('Location: evenements.php?success=created');
                } else {
                    $id = $_POST['id'];
                    $stmt = $db->prepare("UPDATE evenements SET titre = ?, description = ?, date_debut = ?, date_fin = ?, lieu = ?, type_evenement = ?, statut = ? WHERE id = ?");
                    $stmt->execute([$titre, $description, $date_debut, $date_fin, $lieu, $type_evenement, $statut, $id]);
                    header('Location: evenements.php?success=updated');
                }
                exit();
            } catch(PDOException $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    }
}

// Suppression
if (isset($_GET['delete'])) {
    try {
        $stmt = $db->prepare("DELETE FROM evenements WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        header('Location: evenements.php?success=deleted');
        exit();
    } catch(PDOException $e) {
        $error = 'Erreur lors de la suppression: ' . $e->getMessage();
    }
}

// Récupération des données
$action = $_GET['action'] ?? 'list';
$evenement = null;

if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $db->prepare("SELECT * FROM evenements WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $evenement = $stmt->fetch();
    
    if (!$evenement) {
        header('Location: evenements.php?error=not_found');
        exit();
    }
}

// Liste des événements
if ($action === 'list') {
    $stmt = $db->query("SELECT * FROM evenements ORDER BY date_debut DESC");
    $evenements = $stmt->fetchAll();
}

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- Liste des événements -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-calendar-alt"></i>
            Gestion des événements
        </h1>
        <a href="evenements.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Ajouter un événement
        </a>
    </div>

    <!-- Filtres et recherche -->
    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
        <input type="text" id="search-input" placeholder="Rechercher..." class="form-control" style="max-width: 300px;">
        <select class="form-control form-select filter-select" data-filter="type" style="max-width: 200px;">
            <option value="">Tous les types</option>
            <option value="conference">Conférence</option>
            <option value="portes_ouvertes">Portes ouvertes</option>
            <option value="formation">Formation</option>
            <option value="visite">Visite</option>
            <option value="autre">Autre</option>
        </select>
        <select class="form-control form-select filter-select" data-filter="statut" style="max-width: 150px;">
            <option value="">Tous les statuts</option>
            <option value="planifie">Planifié</option>
            <option value="en_cours">En cours</option>
            <option value="termine">Terminé</option>
            <option value="annule">Annulé</option>
        </select>
    </div>

    <!-- Tableau des événements -->
    <table class="admin-table">
        <thead>
            <tr>
                <th>Événement</th>
                <th>Date</th>
                <th>Lieu</th>
                <th>Type</th>
                <th>Statut</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($evenements)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-calendar-alt" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                        Aucun événement pour le moment
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($evenements as $item): ?>
                    <tr data-type="<?php echo $item['type_evenement']; ?>" data-statut="<?php echo $item['statut']; ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($item['titre']); ?></strong>
                            <div style="font-size: 0.9rem; color: var(--gray); margin-top: 0.25rem;">
                                <?php echo substr(strip_tags($item['description']), 0, 100) . '...'; ?>
                            </div>
                        </td>
                        <td>
                            <div style="font-weight: 600;">
                                <?php echo date('d/m/Y', strtotime($item['date_debut'])); ?>
                            </div>
                            <div style="font-size: 0.9rem; color: var(--gray);">
                                <?php echo date('H:i', strtotime($item['date_debut'])); ?>
                                <?php if ($item['date_fin']): ?>
                                    - <?php echo date('H:i', strtotime($item['date_fin'])); ?>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($item['lieu']); ?></td>
                        <td>
                            <span class="status-badge" style="background: var(--light-green); color: var(--primary-color);">
                                <?php 
                                $types = [
                                    'conference' => 'Conférence',
                                    'portes_ouvertes' => 'Portes ouvertes',
                                    'formation' => 'Formation',
                                    'visite' => 'Visite',
                                    'autre' => 'Autre'
                                ];
                                echo $types[$item['type_evenement']] ?? 'Autre';
                                ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $status_colors = [
                                'planifie' => 'info',
                                'en_cours' => 'warning',
                                'termine' => 'success',
                                'annule' => 'danger'
                            ];
                            $status_labels = [
                                'planifie' => 'Planifié',
                                'en_cours' => 'En cours',
                                'termine' => 'Terminé',
                                'annule' => 'Annulé'
                            ];
                            $color = $status_colors[$item['statut']] ?? 'secondary';
                            $label = $status_labels[$item['statut']] ?? 'Inconnu';
                            ?>
                            <span class="status-badge" style="background: var(--<?php echo $color; ?>); color: var(--white);">
                                <?php echo $label; ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="evenements.php?action=edit&id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="evenements.php?delete=<?php echo $item['id']; ?>" 
                                   class="btn btn-sm btn-danger" 
                                   data-action="delete" 
                                   data-item="cet événement">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php else: ?>
<!-- Formulaire d'ajout/modification -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action === 'add' ? 'Ajouter un événement' : 'Modifier l\'événement'; ?>
        </h1>
        <a href="evenements.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <form method="POST" action="">
        <input type="hidden" name="action" value="<?php echo $action; ?>">
        <?php if ($action === 'edit'): ?>
            <input type="hidden" name="id" value="<?php echo $evenement['id']; ?>">
        <?php endif; ?>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
            <div>
                <div class="form-group">
                    <label for="titre" class="form-label">Titre de l'événement *</label>
                    <input type="text" 
                           id="titre" 
                           name="titre" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($evenement['titre'] ?? ''); ?>"
                           placeholder="Ex: Journée Portes Ouvertes 2025">
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">Description *</label>
                    <textarea id="description" 
                              name="description" 
                              class="form-control" 
                              required
                              rows="8"
                              placeholder="Description détaillée de l'événement..."><?php echo htmlspecialchars($evenement['description'] ?? ''); ?></textarea>
                </div>

                <div class="form-group">
                    <label for="lieu" class="form-label">Lieu *</label>
                    <input type="text" 
                           id="lieu" 
                           name="lieu" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($evenement['lieu'] ?? ''); ?>"
                           placeholder="Ex: Amphithéâtre principal">
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="date_debut" class="form-label">Date et heure de début *</label>
                        <input type="datetime-local" 
                               id="date_debut" 
                               name="date_debut" 
                               class="form-control" 
                               required
                               value="<?php echo $evenement ? date('Y-m-d\TH:i', strtotime($evenement['date_debut'])) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="date_fin" class="form-label">Date et heure de fin</label>
                        <input type="datetime-local" 
                               id="date_fin" 
                               name="date_fin" 
                               class="form-control"
                               value="<?php echo $evenement && $evenement['date_fin'] ? date('Y-m-d\TH:i', strtotime($evenement['date_fin'])) : ''; ?>">
                    </div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label for="statut" class="form-label">Statut</label>
                    <select id="statut" name="statut" class="form-control form-select">
                        <option value="planifie" <?php echo ($evenement['statut'] ?? '') === 'planifie' ? 'selected' : ''; ?>>Planifié</option>
                        <option value="en_cours" <?php echo ($evenement['statut'] ?? '') === 'en_cours' ? 'selected' : ''; ?>>En cours</option>
                        <option value="termine" <?php echo ($evenement['statut'] ?? '') === 'termine' ? 'selected' : ''; ?>>Terminé</option>
                        <option value="annule" <?php echo ($evenement['statut'] ?? '') === 'annule' ? 'selected' : ''; ?>>Annulé</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="type_evenement" class="form-label">Type d'événement</label>
                    <select id="type_evenement" name="type_evenement" class="form-control form-select">
                        <option value="conference" <?php echo ($evenement['type_evenement'] ?? '') === 'conference' ? 'selected' : ''; ?>>Conférence</option>
                        <option value="portes_ouvertes" <?php echo ($evenement['type_evenement'] ?? '') === 'portes_ouvertes' ? 'selected' : ''; ?>>Portes ouvertes</option>
                        <option value="formation" <?php echo ($evenement['type_evenement'] ?? '') === 'formation' ? 'selected' : ''; ?>>Formation</option>
                        <option value="visite" <?php echo ($evenement['type_evenement'] ?? '') === 'visite' ? 'selected' : ''; ?>>Visite</option>
                        <option value="autre" <?php echo ($evenement['type_evenement'] ?? '') === 'autre' ? 'selected' : ''; ?>>Autre</option>
                    </select>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer l\'événement' : 'Mettre à jour'; ?>
                    </button>
                </div>

                <?php if ($action === 'edit'): ?>
                    <div class="form-group">
                        <small style="color: var(--gray);">
                            Créé le <?php echo date('d/m/Y à H:i', strtotime($evenement['created_at'])); ?>
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
