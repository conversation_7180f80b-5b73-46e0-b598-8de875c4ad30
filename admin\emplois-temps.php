<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

requireLogin();

$page_title = 'Gestion des emplois du temps - Administration CMC';

// Traitement des actions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add' || $action === 'edit') {
        $formation_id = $_POST['formation_id'] ?? null;
        $groupe = trim($_POST['groupe'] ?? '');
        $jour_semaine = $_POST['jour_semaine'] ?? '';
        $heure_debut = $_POST['heure_debut'] ?? '';
        $heure_fin = $_POST['heure_fin'] ?? '';
        $matiere = trim($_POST['matiere'] ?? '');
        $professeur = trim($_POST['professeur'] ?? '');
        $salle = trim($_POST['salle'] ?? '');
        $type_cours = $_POST['type_cours'] ?? 'cours';
        $semaine_debut = $_POST['semaine_debut'] ?? '';
        $semaine_fin = $_POST['semaine_fin'] ?? '';
        
        if (empty($formation_id) || empty($groupe) || empty($jour_semaine) || empty($heure_debut) || empty($heure_fin) || empty($matiere) || empty($professeur) || empty($salle)) {
            $error = 'Veuillez remplir tous les champs obligatoires.';
        } else {
            try {
                if ($action === 'add') {
                    $stmt = $db->prepare("INSERT INTO emplois_temps (formation_id, groupe, jour_semaine, heure_debut, heure_fin, matiere, professeur, salle, type_cours, semaine_debut, semaine_fin) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$formation_id, $groupe, $jour_semaine, $heure_debut, $heure_fin, $matiere, $professeur, $salle, $type_cours, $semaine_debut, $semaine_fin]);
                    header('Location: emplois-temps.php?success=created');
                } else {
                    $id = $_POST['id'];
                    $stmt = $db->prepare("UPDATE emplois_temps SET formation_id = ?, groupe = ?, jour_semaine = ?, heure_debut = ?, heure_fin = ?, matiere = ?, professeur = ?, salle = ?, type_cours = ?, semaine_debut = ?, semaine_fin = ? WHERE id = ?");
                    $stmt->execute([$formation_id, $groupe, $jour_semaine, $heure_debut, $heure_fin, $matiere, $professeur, $salle, $type_cours, $semaine_debut, $semaine_fin, $id]);
                    header('Location: emplois-temps.php?success=updated');
                }
                exit();
            } catch(PDOException $e) {
                $error = 'Erreur lors de la sauvegarde: ' . $e->getMessage();
            }
        }
    }
}

// Suppression
if (isset($_GET['delete'])) {
    try {
        $stmt = $db->prepare("DELETE FROM emplois_temps WHERE id = ?");
        $stmt->execute([$_GET['delete']]);
        header('Location: emplois-temps.php?success=deleted');
        exit();
    } catch(PDOException $e) {
        $error = 'Erreur lors de la suppression: ' . $e->getMessage();
    }
}

// Récupération des données
$action = $_GET['action'] ?? 'list';
$emploi_temps = null;

if ($action === 'edit' && isset($_GET['id'])) {
    $stmt = $db->prepare("SELECT * FROM emplois_temps WHERE id = ?");
    $stmt->execute([$_GET['id']]);
    $emploi_temps = $stmt->fetch();
    
    if (!$emploi_temps) {
        header('Location: emplois-temps.php?error=not_found');
        exit();
    }
}

// Liste des emplois du temps avec formations
if ($action === 'list') {
    $stmt = $db->query("
        SELECT et.*, f.nom as formation_nom 
        FROM emplois_temps et 
        LEFT JOIN formations f ON et.formation_id = f.id 
        ORDER BY et.jour_semaine, et.heure_debut
    ");
    $emplois_temps = $stmt->fetchAll();
}

// Liste des formations pour le formulaire
$stmt = $db->query("SELECT id, nom FROM formations WHERE statut = 'active' ORDER BY nom");
$formations = $stmt->fetchAll();

include 'includes/header.php';
?>

<?php if ($action === 'list'): ?>
<!-- Liste des emplois du temps -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-clock"></i>
            Gestion des emplois du temps
        </h1>
        <a href="emplois-temps.php?action=add" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Ajouter un créneau
        </a>
    </div>

    <!-- Filtres et recherche -->
    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
        <input type="text" id="search-input" placeholder="Rechercher..." class="form-control" style="max-width: 300px;">
        <select class="form-control form-select filter-select" data-filter="formation" style="max-width: 250px;">
            <option value="">Toutes les formations</option>
            <?php foreach ($formations as $formation): ?>
                <option value="<?php echo $formation['id']; ?>"><?php echo htmlspecialchars($formation['nom']); ?></option>
            <?php endforeach; ?>
        </select>
        <select class="form-control form-select filter-select" data-filter="jour" style="max-width: 150px;">
            <option value="">Tous les jours</option>
            <option value="lundi">Lundi</option>
            <option value="mardi">Mardi</option>
            <option value="mercredi">Mercredi</option>
            <option value="jeudi">Jeudi</option>
            <option value="vendredi">Vendredi</option>
        </select>
    </div>

    <!-- Tableau des emplois du temps -->
    <table class="admin-table">
        <thead>
            <tr>
                <th>Formation / Groupe</th>
                <th>Jour</th>
                <th>Horaires</th>
                <th>Matière</th>
                <th>Professeur</th>
                <th>Salle</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($emplois_temps)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 3rem; color: var(--gray);">
                        <i class="fas fa-clock" style="font-size: 3rem; opacity: 0.3; display: block; margin-bottom: 1rem;"></i>
                        Aucun emploi du temps pour le moment
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($emplois_temps as $item): ?>
                    <tr data-formation="<?php echo $item['formation_id']; ?>" data-jour="<?php echo $item['jour_semaine']; ?>">
                        <td>
                            <strong><?php echo htmlspecialchars($item['formation_nom'] ?? 'Formation supprimée'); ?></strong>
                            <div style="font-size: 0.9rem; color: var(--gray); margin-top: 0.25rem;">
                                <?php echo htmlspecialchars($item['groupe']); ?>
                            </div>
                        </td>
                        <td>
                            <span style="font-weight: 600; text-transform: capitalize;">
                                <?php echo $item['jour_semaine']; ?>
                            </span>
                        </td>
                        <td>
                            <div style="font-weight: 600;">
                                <?php echo date('H:i', strtotime($item['heure_debut'])); ?> - 
                                <?php echo date('H:i', strtotime($item['heure_fin'])); ?>
                            </div>
                            <div style="font-size: 0.8rem; color: var(--gray);">
                                <?php 
                                $duree = (strtotime($item['heure_fin']) - strtotime($item['heure_debut'])) / 3600;
                                echo $duree . 'h';
                                ?>
                            </div>
                        </td>
                        <td>
                            <strong><?php echo htmlspecialchars($item['matiere']); ?></strong>
                            <div style="font-size: 0.8rem; color: var(--primary-color); margin-top: 0.25rem;">
                                <?php echo ucfirst($item['type_cours']); ?>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($item['professeur']); ?></td>
                        <td>
                            <span class="status-badge" style="background: var(--light-green); color: var(--primary-color);">
                                <?php echo htmlspecialchars($item['salle']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="emplois-temps.php?action=edit&id=<?php echo $item['id']; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="emplois-temps.php?delete=<?php echo $item['id']; ?>" 
                                   class="btn btn-sm btn-danger" 
                                   data-action="delete" 
                                   data-item="ce créneau">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php else: ?>
<!-- Formulaire d'ajout/modification -->
<div class="admin-card">
    <div class="card-header">
        <h1 class="card-title">
            <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i>
            <?php echo $action === 'add' ? 'Ajouter un créneau' : 'Modifier le créneau'; ?>
        </h1>
        <a href="emplois-temps.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Retour à la liste
        </a>
    </div>

    <form method="POST" action="">
        <input type="hidden" name="action" value="<?php echo $action; ?>">
        <?php if ($action === 'edit'): ?>
            <input type="hidden" name="id" value="<?php echo $emploi_temps['id']; ?>">
        <?php endif; ?>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
            <div>
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="formation_id" class="form-label">Formation *</label>
                        <select id="formation_id" name="formation_id" class="form-control form-select" required>
                            <option value="">Sélectionnez une formation</option>
                            <?php foreach ($formations as $formation): ?>
                                <option value="<?php echo $formation['id']; ?>" 
                                        <?php echo ($emploi_temps['formation_id'] ?? '') == $formation['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($formation['nom']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="groupe" class="form-label">Groupe *</label>
                        <select id="groupe" name="groupe" class="form-control form-select" required>
                            <option value="">Sélectionnez un groupe</option>
                            <option value="Groupe A" <?php echo ($emploi_temps['groupe'] ?? '') === 'Groupe A' ? 'selected' : ''; ?>>Groupe A</option>
                            <option value="Groupe B" <?php echo ($emploi_temps['groupe'] ?? '') === 'Groupe B' ? 'selected' : ''; ?>>Groupe B</option>
                            <option value="Groupe C" <?php echo ($emploi_temps['groupe'] ?? '') === 'Groupe C' ? 'selected' : ''; ?>>Groupe C</option>
                        </select>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="jour_semaine" class="form-label">Jour *</label>
                        <select id="jour_semaine" name="jour_semaine" class="form-control form-select" required>
                            <option value="">Sélectionnez un jour</option>
                            <option value="lundi" <?php echo ($emploi_temps['jour_semaine'] ?? '') === 'lundi' ? 'selected' : ''; ?>>Lundi</option>
                            <option value="mardi" <?php echo ($emploi_temps['jour_semaine'] ?? '') === 'mardi' ? 'selected' : ''; ?>>Mardi</option>
                            <option value="mercredi" <?php echo ($emploi_temps['jour_semaine'] ?? '') === 'mercredi' ? 'selected' : ''; ?>>Mercredi</option>
                            <option value="jeudi" <?php echo ($emploi_temps['jour_semaine'] ?? '') === 'jeudi' ? 'selected' : ''; ?>>Jeudi</option>
                            <option value="vendredi" <?php echo ($emploi_temps['jour_semaine'] ?? '') === 'vendredi' ? 'selected' : ''; ?>>Vendredi</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="heure_debut" class="form-label">Heure début *</label>
                        <input type="time" 
                               id="heure_debut" 
                               name="heure_debut" 
                               class="form-control" 
                               required
                               value="<?php echo $emploi_temps['heure_debut'] ?? ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="heure_fin" class="form-label">Heure fin *</label>
                        <input type="time" 
                               id="heure_fin" 
                               name="heure_fin" 
                               class="form-control" 
                               required
                               value="<?php echo $emploi_temps['heure_fin'] ?? ''; ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="matiere" class="form-label">Matière *</label>
                    <input type="text" 
                           id="matiere" 
                           name="matiere" 
                           class="form-control" 
                           required
                           value="<?php echo htmlspecialchars($emploi_temps['matiere'] ?? ''); ?>"
                           placeholder="Ex: Agronomie, Biologie végétale...">
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                    <div class="form-group">
                        <label for="professeur" class="form-label">Professeur *</label>
                        <input type="text" 
                               id="professeur" 
                               name="professeur" 
                               class="form-control" 
                               required
                               value="<?php echo htmlspecialchars($emploi_temps['professeur'] ?? ''); ?>"
                               placeholder="Nom du professeur">
                    </div>

                    <div class="form-group">
                        <label for="salle" class="form-label">Salle *</label>
                        <input type="text" 
                               id="salle" 
                               name="salle" 
                               class="form-control" 
                               required
                               value="<?php echo htmlspecialchars($emploi_temps['salle'] ?? ''); ?>"
                               placeholder="Ex: A101, Labo B, Terrain...">
                    </div>
                </div>
            </div>

            <div>
                <div class="form-group">
                    <label for="type_cours" class="form-label">Type de cours</label>
                    <select id="type_cours" name="type_cours" class="form-control form-select">
                        <option value="cours" <?php echo ($emploi_temps['type_cours'] ?? '') === 'cours' ? 'selected' : ''; ?>>Cours</option>
                        <option value="tp" <?php echo ($emploi_temps['type_cours'] ?? '') === 'tp' ? 'selected' : ''; ?>>TP</option>
                        <option value="pratique" <?php echo ($emploi_temps['type_cours'] ?? '') === 'pratique' ? 'selected' : ''; ?>>Pratique</option>
                        <option value="projet" <?php echo ($emploi_temps['type_cours'] ?? '') === 'projet' ? 'selected' : ''; ?>>Projet</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="semaine_debut" class="form-label">Semaine début</label>
                    <input type="date" 
                           id="semaine_debut" 
                           name="semaine_debut" 
                           class="form-control"
                           value="<?php echo $emploi_temps['semaine_debut'] ?? date('Y-m-d'); ?>">
                </div>

                <div class="form-group">
                    <label for="semaine_fin" class="form-label">Semaine fin</label>
                    <input type="date" 
                           id="semaine_fin" 
                           name="semaine_fin" 
                           class="form-control"
                           value="<?php echo $emploi_temps['semaine_fin'] ?? ''; ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        <?php echo $action === 'add' ? 'Créer le créneau' : 'Mettre à jour'; ?>
                    </button>
                </div>

                <?php if ($action === 'edit'): ?>
                    <div class="form-group">
                        <small style="color: var(--gray);">
                            Créé le <?php echo date('d/m/Y à H:i', strtotime($emploi_temps['created_at'])); ?>
                        </small>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </form>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
