// JavaScript pour l'interface d'administration

document.addEventListener('DOMContentLoaded', function() {
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });

    // Confirmation de suppression
    const deleteButtons = document.querySelectorAll('.btn-danger[data-action="delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const itemName = this.getAttribute('data-item') || 'cet élément';
            
            if (confirm(`Êtes-vous sûr de vouloir supprimer ${itemName} ? Cette action est irréversible.`)) {
                window.location.href = this.href;
            }
        });
    });

    // Toggle status
    const statusToggles = document.querySelectorAll('.status-toggle');
    statusToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const currentStatus = this.getAttribute('data-current-status');
            const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
            const itemId = this.getAttribute('data-id');
            const itemType = this.getAttribute('data-type');
            
            // Envoyer la requête AJAX pour changer le statut
            fetch(`ajax/toggle-status.php`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: itemId,
                    type: itemType,
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour l'affichage
                    this.setAttribute('data-current-status', newStatus);
                    const badge = this.querySelector('.status-badge');
                    badge.className = `status-badge status-${newStatus}`;
                    badge.textContent = newStatus === 'active' ? 'Actif' : 'Inactif';
                    
                    // Afficher un message de succès
                    showNotification('Statut mis à jour avec succès', 'success');
                } else {
                    showNotification('Erreur lors de la mise à jour', 'error');
                }
            })
            .catch(error => {
                showNotification('Erreur de connexion', 'error');
            });
        });
    });

    // Recherche en temps réel
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('.admin-table tbody tr');
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Filtres
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            const filterValue = this.value;
            const filterType = this.getAttribute('data-filter');
            const tableRows = document.querySelectorAll('.admin-table tbody tr');
            
            tableRows.forEach(row => {
                if (filterValue === '' || row.getAttribute(`data-${filterType}`) === filterValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });

    // Sélection multiple
    const selectAllCheckbox = document.querySelector('#select-all');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            
            // Mettre à jour le checkbox "Tout sélectionner"
            if (selectAllCheckbox) {
                const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
            }
        });
    });

    // Actions en lot
    const bulkActionButton = document.querySelector('#bulk-action-btn');
    const bulkActionSelect = document.querySelector('#bulk-action-select');
    
    if (bulkActionButton) {
        bulkActionButton.addEventListener('click', function() {
            const selectedItems = Array.from(document.querySelectorAll('.item-checkbox:checked'))
                .map(checkbox => checkbox.value);
            const action = bulkActionSelect.value;
            
            if (selectedItems.length === 0) {
                alert('Veuillez sélectionner au moins un élément.');
                return;
            }
            
            if (!action) {
                alert('Veuillez sélectionner une action.');
                return;
            }
            
            if (confirm(`Êtes-vous sûr de vouloir ${action} ${selectedItems.length} élément(s) ?`)) {
                // Envoyer la requête
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'bulk-actions.php';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = action;
                form.appendChild(actionInput);
                
                selectedItems.forEach(id => {
                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'selected_items[]';
                    idInput.value = id;
                    form.appendChild(idInput);
                });
                
                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    // Prévisualisation d'image
    const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            const preview = document.querySelector(`#${this.id}-preview`);
            
            if (file && preview) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    });

    // Éditeur de texte simple
    const textareas = document.querySelectorAll('textarea[data-editor="simple"]');
    textareas.forEach(textarea => {
        // Ajouter une barre d'outils simple
        const toolbar = document.createElement('div');
        toolbar.className = 'editor-toolbar';
        toolbar.innerHTML = `
            <button type="button" data-action="bold"><i class="fas fa-bold"></i></button>
            <button type="button" data-action="italic"><i class="fas fa-italic"></i></button>
            <button type="button" data-action="underline"><i class="fas fa-underline"></i></button>
        `;
        
        textarea.parentNode.insertBefore(toolbar, textarea);
        
        // Gérer les actions de la barre d'outils
        toolbar.addEventListener('click', function(e) {
            if (e.target.closest('button')) {
                e.preventDefault();
                const action = e.target.closest('button').getAttribute('data-action');
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                
                let replacement = selectedText;
                switch(action) {
                    case 'bold':
                        replacement = `**${selectedText}**`;
                        break;
                    case 'italic':
                        replacement = `*${selectedText}*`;
                        break;
                    case 'underline':
                        replacement = `_${selectedText}_`;
                        break;
                }
                
                textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
                textarea.focus();
                textarea.setSelectionRange(start + replacement.length, start + replacement.length);
            }
        });
    });
});

// Fonctions utilitaires
function updateBulkActions() {
    const selectedCount = document.querySelectorAll('.item-checkbox:checked').length;
    const bulkActionsContainer = document.querySelector('#bulk-actions');
    
    if (bulkActionsContainer) {
        if (selectedCount > 0) {
            bulkActionsContainer.style.display = 'block';
            document.querySelector('#selected-count').textContent = selectedCount;
        } else {
            bulkActionsContainer.style.display = 'none';
        }
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
        ${message}
    `;
    
    const main = document.querySelector('.admin-main');
    main.insertBefore(notification, main.firstChild);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Validation de formulaire
function validateForm(formId) {
    const form = document.getElementById(formId);
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc3545';
            isValid = false;
        } else {
            field.style.borderColor = '#e9ecef';
        }
    });
    
    return isValid;
}

// Auto-save pour les brouillons
function autoSave(formId) {
    const form = document.getElementById(formId);
    if (!form) return;
    
    const formData = new FormData(form);
    formData.append('auto_save', '1');
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Brouillon sauvegardé automatiquement', 'success');
        }
    })
    .catch(error => {
        console.log('Erreur auto-save:', error);
    });
}

// Initialiser l'auto-save toutes les 30 secondes
setInterval(() => {
    const forms = document.querySelectorAll('form[data-auto-save="true"]');
    forms.forEach(form => {
        autoSave(form.id);
    });
}, 30000);
